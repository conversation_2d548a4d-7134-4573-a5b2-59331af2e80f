<?php
#region DOCS

/** @var array $cierres Lista de cierres (inicialmente vacía, se llena via AJAX) */
/** @var array $centros_costos Lista de centros de costo activos para filtro */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Listado de Cierres de Caja</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
        <!-- BEGIN container -->
        <div class="container">
            <!-- BEGIN row -->
            <div class="row justify-content-center">
                <!-- BEGIN col-12 -->
                <div class="col-12">

            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center mb-3">
                <div>
                    <h4 class="mb-0">Listado de Cierres de Caja</h4>
                    <p class="mb-0 text-muted">Consulta de cierres de caja realizados</p>
                </div>
                <div class="ms-auto">
                    <a href="cerrar-caja" class="btn btn-success">
                        <i class="fa fa-cash-register fa-fw me-1"></i> Cerrar Caja
                    </a>
                </div>
            </div>

            <hr>
            <?php #endregion PAGE HEADER ?>

            <!-- BEGIN filters-panel -->
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">Filtros de Búsqueda</h4>
                </div>
                <div class="panel-body">
                    <form id="filtros-form" class="row g-3">
                        <!-- Date Range Filters -->
                        <div class="col-md-3">
                            <label for="fecha_desde" class="form-label">Fecha Desde <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_desde" name="fecha_desde"
                                       placeholder="yyyy-mm-dd" required>
                                <span class="input-group-text" id="fecha_desde_icon" style="cursor: pointer;">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <label for="fecha_hasta" class="form-label">Fecha Hasta <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_hasta" name="fecha_hasta"
                                       placeholder="yyyy-mm-dd" required>
                                <span class="input-group-text" id="fecha_hasta_icon" style="cursor: pointer;">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                        </div>

                        <!-- Centro Costo Filter -->
                        <div class="col-md-4">
                            <label for="filtro_centro_costo" class="form-label">Centro de Costo</label>
                            <select class="form-select" id="filtro_centro_costo" name="filtro_centro_costo">
                                <option value="">Todos los centros</option>
                                <?php foreach ($centros_costos as $centro): ?>
                                    <option value="<?php echo $centro->getId(); ?>">
                                        <?php echo htmlspecialchars($centro->getNombre()); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Search Button -->
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fa fa-chart-line"></i> Generar Reporte
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- END filters-panel -->

            <!-- BEGIN main-content-panel -->
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">Resultados de Búsqueda</h4>
                </div>
                <div class="panel-body p-0">
                    <!-- Empty state message (shown by default) -->
                    <div id="empty-state" class="text-center p-5">
                        <i class="fa fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Utilice los filtros para consultar los cierres de caja</h5>
                        <p class="text-muted">Seleccione un rango de fechas para ver los cierres realizados. Opcionalmente puede filtrar por centro de costo.</p>
                    </div>

                    <!-- Loading overlay -->
                    <div id="loading-overlay" style="display: none; position: relative; min-height: 200px;">
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Results table -->
                    <div id="results-table-container" style="display: none; overflow-x: auto;">
                        <table class="table table-hover table-sm mb-0" id="cierres-table">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 60px;">#</th>
                                    <th style="width: 120px;">Fecha</th>
                                    <th>Centro de Costo</th>
                                    <th class="text-end" style="width: 140px;">Total Citas</th>
                                    <th class="text-end" style="width: 140px;">Comisiones</th>
                                    <th class="text-end" style="width: 140px;">Total Ventas</th>
                                    <th class="text-end" style="width: 140px;">Órdenes Compra</th>
                                    <th class="text-end" style="width: 140px;">Gastos Operativos</th>
                                </tr>
                            </thead>
                            <tbody id="cierres-table-body">
                                <!-- Results will be populated via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- No results message -->
                    <div id="no-results" style="display: none;" class="text-center p-4">
                        <i class="fa fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No se encontraron cierres</h5>
                        <p class="text-muted">No hay cierres de caja para los criterios de búsqueda seleccionados</p>
                    </div>
                </div>
            </div>
            <!-- END main-content-panel -->
        </div>
        <!-- END container -->
    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

<!-- ================== BEGIN core-js ================== -->
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>
<!-- ================== END core-js ================== -->

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_desde_icon').addEventListener('click', function() {
        $('#fecha_desde').datepicker('show');
    });

    document.getElementById('fecha_hasta_icon').addEventListener('click', function() {
        $('#fecha_hasta').datepicker('show');
    });

    // Elements
    const filtrosForm           = document.getElementById('filtros-form');
    const emptyState            = document.getElementById('empty-state');
    const loadingOverlay        = document.getElementById('loading-overlay');
    const resultsTableContainer = document.getElementById('results-table-container');
    const cierresTableBody      = document.getElementById('cierres-table-body');
    const noResults             = document.getElementById('no-results');

    // Handle form submission
    filtrosForm.addEventListener('submit', function(event) {
        event.preventDefault();
        buscarCierres();
    });

    // Search function
    function buscarCierres() {
        const formData = new FormData(filtrosForm);

        // Validate required fields
        const fechaDesde = document.getElementById('fecha_desde').value;
        const fechaHasta = document.getElementById('fecha_hasta').value;

        if (!fechaDesde || !fechaHasta) {
            showError('Las fechas desde y hasta son obligatorias para realizar la consulta.');
            return;
        }

        // Show loading
        emptyState.style.display            = 'none';
        loadingOverlay.style.display        = 'block';
        resultsTableContainer.style.display = 'none';
        noResults.style.display             = 'none';

        // Add action to form data
        formData.append('action', 'search_cierres');

        // Submit search
        fetch('listado-cierres', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingOverlay.style.display = 'none';

            if (data.success) {
                if (data.cierres && data.cierres.length > 0) {
                    displayResults(data.cierres);
                } else {
                    showNoResults();
                }
            } else {
                showError(data.message || 'Error al buscar cierres');
            }
        })
        .catch(error => {
            loadingOverlay.style.display = 'none';
            showError('Error de conexión al buscar cierres');
            console.error('Error:', error);
        });
    }

    // Display results in table
    function displayResults(cierres) {
        cierresTableBody.innerHTML = '';

        cierres.forEach(cierre => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${cierre.id}</td>
                <td>${cierre.fecha_formateada}</td>
                <td>${cierre.centro_costo_nombre}</td>
                <td class="text-end">${cierre.valor_total_citas_formateado}</td>
                <td class="text-end">${cierre.valor_comision_total_citas_formateado}</td>
                <td class="text-end">${cierre.valor_total_ventas_formateado}</td>
                <td class="text-end">${cierre.valor_total_ordenes_compra_formateado}</td>
                <td class="text-end">${cierre.valor_total_gastos_operativos_formateado}</td>
            `;
            cierresTableBody.appendChild(row);
        });

        resultsTableContainer.style.display = 'block';
        noResults.style.display = 'none';
    }

    // Show no results message
    function showNoResults() {
        resultsTableContainer.style.display = 'none';
        noResults.style.display = 'block';
    }

    // Show error message using SweetAlert
    function showError(message) {
        if (typeof showSweetAlertError === 'function') {
            showSweetAlertError('Error', message);
        } else {
            // Fallback to basic alert if SweetAlert is not available
            alert('Error: ' + message);
        }
    }
});
</script>
</body>
</html>
